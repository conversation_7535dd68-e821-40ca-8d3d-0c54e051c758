import org.kefir.services.DocBuilder
import org.kefir.services.NpmService
import org.kefir.services.ArtifactService
import org.kefir.services.NotificationService

def call(Map config) {
    def npmService = new NpmService(this)
    def docBuilder = new DocBuilder(this)
    def artifactService = new ArtifactService(this)
    def notificationService = new NotificationService(this)

    pipeline {
        agent { node { label config.agentLabel ?: jenkinsUtils.determineAgent() } }

        stages {
            stage('Публикация приложения') {
                when {
                    expression { config.app.enabled }
                }
                steps {
                    script {
                        def publishResult = npmService.publishNpm([
                            verdaccioInstance: config.app.verdaccioInstance,
                            nodeJsTool: config.app.nodeJsTool
                        ])

                        // Store publish result for later use in notifications
                        env.NPM_PUBLISH_RESULT = writeJSON returnText: true, json: publishResult
                    }
                }
            }
            stage('Создание ZIP архива') {
                when {
                    expression { config.app.enabled && config.app.createZipArtifact }
                }
                steps {
                    script {
                        artifactService.createZipArtifact([
                            nodeJsTool: config.app.nodeJsTool
                        ])
                    }
                }
            }
            stage('Публикация документации в Confluence') {
                when {
                    expression { config.docs.enabled }
                }
                steps {
                    script {
                        docBuilder.publishDocs([
                            confluenceSpaceName: config.docs.confluenceSpaceName,
                            confluenceParentPageID: config.docs.confluenceParentPageID,
                            documentationDirectoryPath: config.docs.documentationDirectoryPath,
                            documentationLabel: config.docs.documentationLabel,
                            documentationTitle: config.docs.documentationTitle,
                            documentationHomePagePath: config.docs.documentationHomePagePath
                        ])
                    }
                }
            }
            stage('Уведомления о развертывании') {
                when {
                    expression {
                        config.app.enabled &&
                        config.notifications?.enabled &&
                        env.NPM_PUBLISH_RESULT
                    }
                }
                steps {
                    script {
                        def publishResult = readJSON text: env.NPM_PUBLISH_RESULT
                        if (publishResult.success) {
                            notificationService.sendDeploymentNotification([
                                channel: config.notifications.mattermostChannel ?: '#deployments',
                                includeChangelog: config.notifications.includeChangelog ?: true,
                                packageInfo: publishResult
                            ])
                        }
                    }
                }
            }
        }
    }
}