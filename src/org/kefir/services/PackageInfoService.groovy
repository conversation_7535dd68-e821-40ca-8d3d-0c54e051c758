package org.kefir.services

class PackageInfoService {
    private final def script

    PackageInfoService(script) {
        this.script = script
    }

    /**
     * Gets package information from package.json
     * @param nodeJsTool Node.js tool name for environment setup (optional)
     * @return Map with package information
     */
    Map getPackageInfo(String nodeJsTool = null) {
        try {
            if (nodeJsTool) {
                script.withEnv(["PATH+NODE=${script.tool name: nodeJsTool, type: 'nodejs'}/bin"]) {
                    return readPackageJson()
                }
            } else {
                return readPackageJson()
            }
        } catch (Exception e) {
            return [success: false, error: "Error reading package.json: ${e.message}"]
        }
    }

    /**
     * Reads and parses package.json file
     * @return Map with package details
     */
    private Map readPackageJson() {
        if (!script.fileExists('package.json')) {
            return [success: false, error: "package.json не найден в рабочей директории"]
        }

        def packageJson = script.readJSON file: 'package.json'

        if (!packageJson.name) {
            return [success: false, error: "Поле 'name' не найдено в package.json"]
        }

        if (!packageJson.version) {
            return [success: false, error: "Поле 'version' не найдено в package.json"]
        }

        // Extract repository name from package name (remove prefix if present)
        String packageName = packageJson.name
        String repositoryName = packageName.startsWith('com.kefir.') ? 
            packageName.substring('com.kefir.'.length()) : packageName

        return [
            success: true,
            repositoryName: repositoryName,
            version: packageJson.version,
            packageName: packageName,
            fullPackageJson: packageJson
        ]
    }

    /**
     * Gets only package name and version (simplified version)
     * @return Map with name and version
     */
    Map getBasicPackageInfo() {
        if (!script.fileExists('package.json')) {
            throw new Exception("package.json не найден в рабочей директории")
        }

        def packageJson = script.readJSON file: 'package.json'

        if (!packageJson.name) {
            throw new Exception("Поле 'name' не найдено в package.json")
        }

        if (!packageJson.version) {
            throw new Exception("Поле 'version' не найдено в package.json")
        }

        return [
            name: packageJson.name,
            version: packageJson.version
        ]
    }
}
