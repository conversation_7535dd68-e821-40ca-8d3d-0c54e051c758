package org.kefir.services

class NpmService {
    private final def script
    private final PackageInfoService packageInfoService

    NpmService(script) {
        this.script = script
        this.packageInfoService = new PackageInfoService(script)
    }

    Map publishNpm(Map config) {
        try {
            // Get package information before publishing
            def packageInfo = packageInfoService.getBasicPackageInfo()

            script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
                script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                    script.sh('npm config fix && npm publish')
                }
            }

            script.echo "Пакет ${packageInfo.name}@${packageInfo.version} успешно опубликован в ${config.verdaccioInstance}"

            return [
                success: true,
                log: "Пакет загружен в ${config.verdaccioInstance}",
                packageName: packageInfo.name,
                version: packageInfo.version,
                verdaccioInstance: config.verdaccioInstance
            ]

        } catch (Exception e) {
            script.error("Ошибка при публикации NPM пакета: ${e.message}")
            return [
                success: false,
                error: e.message,
                log: "Ошибка публикации в ${config.verdaccioInstance}"
            ]
        }
    }

    void unpublishNpm(Map config) {
        script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
            script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                script.sh("npm config fix && npm unpublish --force ${config.packageName}")
            }
            return [success: true, log: "Пакет ${config.packageName} удален из ${config.verdaccioInstance}"]
        }
    }


}
