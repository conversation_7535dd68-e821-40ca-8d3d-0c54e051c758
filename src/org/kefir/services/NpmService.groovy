package org.kefir.services

class NpmService {
    private final def script

    NpmService(script) {
        this.script = script
    }

    Map publishNpm(Map config) {
        try {
            // Get package information before publishing
            def packageInfo = getPackageInfo()

            script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
                script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                    script.sh('npm config fix && npm publish')
                }
            }

            script.echo "Пакет ${packageInfo.name}@${packageInfo.version} успешно опубликован в ${config.verdaccioInstance}"

            return [
                success: true,
                log: "Пакет загружен в ${config.verdaccioInstance}",
                packageName: packageInfo.name,
                version: packageInfo.version,
                verdaccioInstance: config.verdaccioInstance
            ]

        } catch (Exception e) {
            script.error("Ошибка при публикации NPM пакета: ${e.message}")
            return [
                success: false,
                error: e.message,
                log: "Ошибка публикации в ${config.verdaccioInstance}"
            ]
        }
    }

    void unpublishNpm(Map config) {
        script.configFileProvider([script.configFile(fileId: config.verdaccioInstance, targetLocation: '.npmrc')]) {
            script.withEnv(["PATH+NODE=${script.tool name: config.nodeJsTool, type: 'nodejs'}/bin"]) {
                script.sh("npm config fix && npm unpublish --force ${config.packageName}")
            }
            return [success: true, log: "Пакет ${config.packageName} удален из ${config.verdaccioInstance}"]
        }
    }

    /**
     * Gets package information from package.json
     * @return Map with package name and version
     */
    private Map getPackageInfo() {
        if (!script.fileExists('package.json')) {
            throw new Exception("package.json не найден в рабочей директории")
        }

        def packageJson = script.readJSON file: 'package.json'

        if (!packageJson.name) {
            throw new Exception("Поле 'name' не найдено в package.json")
        }

        if (!packageJson.version) {
            throw new Exception("Поле 'version' не найдено в package.json")
        }

        return [
            name: packageJson.name,
            version: packageJson.version
        ]
    }
}
