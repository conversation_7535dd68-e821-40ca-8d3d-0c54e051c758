package org.kefir.services

import org.kefir.utils.ChangelogUtils

class NotificationService {
    private final def script

    NotificationService(script) {
        this.script = script
    }

    /**
     * Sends deployment notification to Mattermost channel
     * @param config Configuration map with channel, includeChangelog, and packageInfo
     * @return Map with success status and details
     */
    Map sendDeploymentNotification(Map config) {
        try {
            script.echo "Отправка уведомления о развертывании в Mattermost..."
            
            // Get package information
            def packageInfo = getPackageInformation()
            if (!packageInfo.success) {
                script.echo "Предупреждение: Не удалось получить информацию о пакете: ${packageInfo.error}"
                // Continue with basic notification
            }
            
            // Build notification message
            String message = buildNotificationMessage(packageInfo, config)
            
            // Send notification using existing slackUtils (which uses Mattermost)
            def result = script.slackUtils.shortStatus([
                channel: config.channel ?: '#deployments',
                message: message
            ])
            
            if (result) {
                script.echo "Уведомление успешно отправлено в канал ${config.channel ?: '#deployments'}"
                return [success: true, log: "Notification sent successfully"]
            } else {
                return [success: false, error: "Failed to send notification"]
            }
            
        } catch (Exception e) {
            script.echo "Ошибка при отправке уведомления: ${e.message}"
            return [success: false, error: e.message]
        }
    }
    
    /**
     * Gets package information from package.json
     * @return Map with package details
     */
    private Map getPackageInformation() {
        try {
            if (!script.fileExists('package.json')) {
                return [success: false, error: "package.json not found"]
            }
            
            def packageJson = script.readJSON file: 'package.json'
            
            String packageName = packageJson.name ?: 'Unknown Package'
            String version = packageJson.version ?: 'Unknown Version'
            
            // Extract plugin name from package name (remove com.kefir. prefix if present)
            String pluginName = packageName.startsWith('com.kefir.') ? 
                packageName.substring('com.kefir.'.length()) : packageName
            
            return [
                success: true,
                packageName: packageName,
                pluginName: pluginName,
                version: version
            ]
            
        } catch (Exception e) {
            return [success: false, error: "Error reading package.json: ${e.message}"]
        }
    }
    
    /**
     * Builds the notification message for Mattermost
     * @param packageInfo Package information map
     * @param config Configuration map
     * @return Formatted message string
     */
    private String buildNotificationMessage(Map packageInfo, Map config) {
        StringBuilder message = new StringBuilder()
        
        // Basic deployment message
        if (packageInfo.success) {
            message.append("🚀 **The ${packageInfo.pluginName} plugin has been updated to version ${packageInfo.version}**\n\n")
        } else {
            message.append("🚀 **Plugin has been deployed**\n\n")
        }
        
        // Add changelog if requested and available
        if (config.includeChangelog && packageInfo.success) {
            String changelogSection = getChangelogForVersion(packageInfo.version)
            if (changelogSection) {
                message.append(changelogSection)
            } else {
                message.append("_Changelog information not available for this version._\n")
            }
        }
        
        // Add deployment details
        message.append("\n---\n")
        message.append("**Deployment Details:**\n")
        message.append("• **Job:** ${script.env.JOB_NAME}\n")
        message.append("• **Build:** [#${script.env.BUILD_NUMBER}](${script.env.BUILD_URL})\n")
        
        if (script.env.GIT_COMMIT) {
            String shortCommit = script.env.GIT_COMMIT.take(8)
            message.append("• **Commit:** `${shortCommit}`\n")
        }
        
        if (script.env.GIT_BRANCH) {
            message.append("• **Branch:** `${script.env.GIT_BRANCH}`\n")
        }
        
        return message.toString()
    }
    
    /**
     * Extracts changelog information for a specific version
     * @param version Version to get changelog for
     * @return Formatted changelog string or null if not available
     */
    private String getChangelogForVersion(String version) {
        try {
            // Validate changelog file exists
            def validation = ChangelogUtils.validateChangelogFile(script)
            if (!validation.success) {
                script.echo "Changelog file validation failed: ${validation.error}"
                return null
            }
            
            // Extract changelog for the specific version
            def changelogResult = ChangelogUtils.extractVersionChangelog(validation.content, version)
            if (!changelogResult.success) {
                script.echo "Could not extract changelog for version ${version}: ${changelogResult.error}"
                return null
            }
            
            // Format for Mattermost
            return ChangelogUtils.formatForMattermost(changelogResult.changelog, version)
            
        } catch (Exception e) {
            script.echo "Error processing changelog: ${e.message}"
            return null
        }
    }
    
    /**
     * Sends a simple deployment notification without changelog
     * @param config Configuration map with channel and basic message info
     * @return Map with success status
     */
    Map sendSimpleNotification(Map config) {
        try {
            String message = config.message ?: "🚀 **Deployment completed successfully**"
            
            def result = script.slackUtils.shortStatus([
                channel: config.channel ?: '#deployments',
                message: message
            ])
            
            return [success: result != null, log: "Simple notification sent"]
            
        } catch (Exception e) {
            script.echo "Error sending simple notification: ${e.message}"
            return [success: false, error: e.message]
        }
    }
}
