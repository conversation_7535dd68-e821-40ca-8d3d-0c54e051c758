package org.kefir.services

class ArtifactService {
    private final def script
    private final PackageInfoService packageInfoService

    ArtifactService(script) {
        this.script = script
        this.packageInfoService = new PackageInfoService(script)
    }

    /**
     * Creates a ZIP archive of build artifacts with proper naming and structure
     * @param config Configuration map containing nodeJsTool
     * @return Map with success status and details
     */
    Map createZipArtifact(Map config) {
        try {
            script.echo "Создание ZIP архива артефактов..."
            
            // Read package.json to get repository name and version
            def packageInfo = packageInfoService.getPackageInfo(config.nodeJsTool)
            
            if (!packageInfo.success) {
                return [success: false, error: packageInfo.error]
            }
            
            String repositoryName = packageInfo.repositoryName
            String version = packageInfo.version
            String zipFileName = "${repositoryName}-${version}.zip"
            
            script.echo "Создание архива: ${zipFileName}"
            script.echo "Структура архива: ${repositoryName}/ (корневая папка)"
            
            // Create the ZIP archive with proper structure
            createZipWithStructure(repositoryName, zipFileName)
            
            // Archive the ZIP file as Jenkins artifact
            script.archiveArtifacts artifacts: zipFileName, fingerprint: true
            
            script.echo "ZIP архив успешно создан и сохранен как артефакт: ${zipFileName}"
            
            return [
                success: true, 
                zipFileName: zipFileName,
                repositoryName: repositoryName,
                version: version,
                log: "ZIP архив ${zipFileName} успешно создан"
            ]
            
        } catch (Exception e) {
            script.error("Ошибка при создании ZIP архива: ${e.message}")
            return [success: false, error: e.message]
        }
    }
    

    
    /**
     * Creates ZIP archive with proper directory structure
     * @param repositoryName Name of the repository (used as root folder)
     * @param zipFileName Name of the output ZIP file
     */
    private void createZipWithStructure(String repositoryName, String zipFileName) {
        script.sh """
            # Create temporary directory for archive structure
            mkdir -p temp_archive/${repositoryName}
            
            # Copy all addon files to the repository folder, excluding build artifacts and temp files
            rsync -av --exclude='temp_archive' \\
                     --exclude='*.zip' \\
                     --exclude='node_modules' \\
                     --exclude='.git' \\
                     --exclude='.gitignore' \\
                     --exclude='*.log' \\
                     --exclude='.npmrc' \\
                     ./ temp_archive/${repositoryName}/
            
            # Create ZIP archive
            cd temp_archive
            zip -r ../${zipFileName} ${repositoryName}/
            cd ..
            
            # Clean up temporary directory
            rm -rf temp_archive
            
            # Verify ZIP was created
            if [ ! -f "${zipFileName}" ]; then
                echo "Ошибка: ZIP файл не был создан"
                exit 1
            fi
            
            # Show ZIP contents for verification
            echo "Содержимое архива ${zipFileName}:"
            unzip -l ${zipFileName} | head -20
        """
    }
}
