package org.kefir.services

class ArtifactService {
    private final def script

    ArtifactService(script) {
        this.script = script
    }

    /**
     * Creates a ZIP archive of build artifacts with proper naming and structure
     * @param config Configuration map containing nodeJsTool
     * @return Map with success status and details
     */
    Map createZipArtifact(Map config) {
        try {
            script.echo "Создание ZIP архива артефактов..."
            
            // Read package.json to get repository name and version
            def packageInfo = getPackageInfo(config.nodeJsTool)
            
            if (!packageInfo.success) {
                return [success: false, error: packageInfo.error]
            }
            
            String repositoryName = packageInfo.repositoryName
            String version = packageInfo.version
            String zipFileName = "${repositoryName}-${version}.zip"
            
            script.echo "Создание архива: ${zipFileName}"
            script.echo "Структура архива: ${repositoryName}/ (корневая папка)"
            
            // Create the ZIP archive with proper structure
            createZipWithStructure(repositoryName, zipFileName)
            
            // Archive the ZIP file as Jenkins artifact
            script.archiveArtifacts artifacts: zipFileName, fingerprint: true
            
            script.echo "ZIP архив успешно создан и сохранен как артефакт: ${zipFileName}"
            
            return [
                success: true, 
                zipFileName: zipFileName,
                repositoryName: repositoryName,
                version: version,
                log: "ZIP архив ${zipFileName} успешно создан"
            ]
            
        } catch (Exception e) {
            script.error("Ошибка при создании ZIP архива: ${e.message}")
            return [success: false, error: e.message]
        }
    }
    
    /**
     * Reads package.json and extracts repository name and version
     * @param nodeJsTool Node.js tool name for environment setup
     * @return Map with package information
     */
    private Map getPackageInfo(String nodeJsTool) {
        try {
            script.withEnv(["PATH+NODE=${script.tool name: nodeJsTool, type: 'nodejs'}/bin"]) {
                // Check if package.json exists
                if (!script.fileExists('package.json')) {
                    return [success: false, error: "package.json не найден в рабочей директории"]
                }
                
                // Read and parse package.json
                def packageJson = script.readJSON file: 'package.json'
                
                if (!packageJson.name) {
                    return [success: false, error: "Поле 'name' не найдено в package.json"]
                }
                
                if (!packageJson.version) {
                    return [success: false, error: "Поле 'version' не найдено в package.json"]
                }
                
                // Extract repository name from package name (remove prefix if present)
                String packageName = packageJson.name
                String repositoryName = packageName.startsWith('com.kefir.') ? 
                    packageName.substring('com.kefir.'.length()) : packageName
                
                return [
                    success: true,
                    repositoryName: repositoryName,
                    version: packageJson.version,
                    packageName: packageName
                ]
            }
        } catch (Exception e) {
            return [success: false, error: "Ошибка чтения package.json: ${e.message}"]
        }
    }
    
    /**
     * Creates ZIP archive with proper directory structure
     * @param repositoryName Name of the repository (used as root folder)
     * @param zipFileName Name of the output ZIP file
     */
    private void createZipWithStructure(String repositoryName, String zipFileName) {
        script.sh """
            # Create temporary directory for archive structure
            mkdir -p temp_archive/${repositoryName}
            
            # Copy all addon files to the repository folder, excluding build artifacts and temp files
            rsync -av --exclude='temp_archive' \\
                     --exclude='*.zip' \\
                     --exclude='node_modules' \\
                     --exclude='.git' \\
                     --exclude='.gitignore' \\
                     --exclude='*.log' \\
                     --exclude='.npmrc' \\
                     ./ temp_archive/${repositoryName}/
            
            # Create ZIP archive
            cd temp_archive
            zip -r ../${zipFileName} ${repositoryName}/
            cd ..
            
            # Clean up temporary directory
            rm -rf temp_archive
            
            # Verify ZIP was created
            if [ ! -f "${zipFileName}" ]; then
                echo "Ошибка: ZIP файл не был создан"
                exit 1
            fi
            
            # Show ZIP contents for verification
            echo "Содержимое архива ${zipFileName}:"
            unzip -l ${zipFileName} | head -20
        """
    }
}
