package org.kefir.validators

class ChangelogValidator extends BaseValidator {
    private String changelogContent
    private boolean initialized = false

    ChangelogValidator(Script script) {
        super(script)
    }

    private void ensureInitialized() {
        if (!initialized) {
            try {
                def validation = validateChangelogFile()
                if (validation.success) {
                    this.changelogContent = validation.content
                }
                initialized = true
            } catch (Exception e) {
                script.error("Ошибка инициализации ChangelogValidator: ${e.message}")
                throw e
            }
        }
    }

    /**
     * Validates if changelog file exists and is readable
     * @param changelogPath Path to the changelog file (default: 'CHANGELOG.md')
     * @return Map with validation result
     */
    Map validateChangelogFile(String changelogPath = 'CHANGELOG.md') {
        try {
            if (!script.fileExists(changelogPath)) {
                return [success: false, error: "Changelog file not found: ${changelogPath}"]
            }

            String content = script.readFile(changelogPath)
            if (!content || content.trim().isEmpty()) {
                return [success: false, error: "Changelog file is empty: ${changelogPath}"]
            }

            return [success: true, content: content]

        } catch (Exception e) {
            return [success: false, error: "Error reading changelog file: ${e.message}"]
        }
    }

    /**
     * Extracts changelog entry for a specific version
     * @param version The version to extract changelog for
     * @return Map with success status and changelog entry
     */
    Map extractVersionChangelog(String version) {
        ensureInitialized()

        if (!changelogContent || !version) {
            return [success: false, error: "Changelog content or version is empty"]
        }

        try {
            // Normalize version (remove 'v' prefix if present)
            String normalizedVersion = version.replaceFirst(/^[vV]/, '')

            // Pattern to match version headers (## 1.2.3, ## [1.2.3], etc.)
            String versionPattern = /(?m)^##\s*(?:\[)?${java.util.regex.Pattern.quote(normalizedVersion)}(?:\])?.*$/

            // Find the start of the target version section
            def versionMatcher = changelogContent =~ versionPattern
            if (!versionMatcher.find()) {
                return [success: false, error: "Version ${version} not found in changelog"]
            }

            int versionStartIndex = versionMatcher.start()

            // Find the next version header to determine where this version's changelog ends
            String nextVersionPattern = /(?m)^##\s+(?:\[)?(?:\d+\.\d+\.\d+|Unreleased)/
            def nextVersionMatcher = changelogContent =~ nextVersionPattern

            int versionEndIndex = changelogContent.length()

            // Look for the next version header after our target version
            while (nextVersionMatcher.find()) {
                if (nextVersionMatcher.start() > versionStartIndex) {
                    versionEndIndex = nextVersionMatcher.start()
                    break
                }
            }

            // Extract the changelog section for this version
            String versionChangelog = changelogContent.substring(versionStartIndex, versionEndIndex).trim()

            // Clean up the changelog entry
            String cleanedChangelog = cleanupChangelogEntry(versionChangelog)

            return [
                success: true,
                changelog: cleanedChangelog,
                version: normalizedVersion
            ]

        } catch (Exception e) {
            return [success: false, error: "Error parsing changelog: ${e.message}"]
        }
    }

    /**
     * Formats changelog content for Mattermost (Markdown format)
     * @param changelog Raw changelog content
     * @param version Version number
     * @return Formatted changelog for Mattermost
     */
    String formatForMattermost(String changelog, String version) {
        if (!changelog || changelog.trim().isEmpty()) {
            return "Changelog для версии ${version} не найден."
        }

        StringBuilder formatted = new StringBuilder()
        formatted.append("**Changelog для версии ${version}:**\n\n")

        // Process each line to ensure proper Markdown formatting
        List<String> lines = changelog.split('\n')

        for (String line : lines) {
            String trimmedLine = line.trim()

            if (trimmedLine.isEmpty()) {
                formatted.append('\n')
                continue
            }

            // Format section headers (### Features, ### Fixes, etc.)
            if (trimmedLine.startsWith('###')) {
                formatted.append("**${trimmedLine.substring(3).trim()}:**\n")
                continue
            }

            // Format bullet points
            if (trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
                formatted.append("• ${trimmedLine.substring(1).trim()}\n")
                continue
            }

            // Regular lines
            formatted.append("${trimmedLine}\n")
        }

        return formatted.toString()
    }

    /**
     * Cleans up and formats a changelog entry for better readability
     * @param rawChangelog Raw changelog entry text
     * @return Cleaned and formatted changelog text
     */
    private String cleanupChangelogEntry(String rawChangelog) {
        if (!rawChangelog) {
            return ""
        }

        // Split into lines for processing
        List<String> lines = rawChangelog.split('\n').collect { it.trim() }
        List<String> cleanedLines = []

        boolean inChangelogContent = false

        for (String line : lines) {
            // Skip empty lines at the beginning
            if (!inChangelogContent && line.isEmpty()) {
                continue
            }

            // Skip the version header line (starts with ##)
            if (line.startsWith('##')) {
                inChangelogContent = true
                continue
            }

            // Skip date lines that immediately follow version headers
            if (inChangelogContent && line.matches(/^\d{4}-\d{2}-\d{2}$/)) {
                continue
            }

            // Include all other content
            if (inChangelogContent) {
                cleanedLines.add(line)
            }
        }

        // Remove trailing empty lines
        while (cleanedLines.size() > 0 && cleanedLines.last().isEmpty()) {
            cleanedLines.removeLast()
        }

        return cleanedLines.join('\n')
    }

    /**
     * Checks if changelog exists and contains valid format
     * @return Map with validation result
     */
    Map checkChangelogFormat() {
        def validation = validateChangelogFile()
        if (!validation.success) {
            return createResult(false, validation.error)
        }

        // Basic format validation - check if it has version headers
        String content = validation.content
        if (!content.contains('##')) {
            return createResult(false, "Changelog не содержит заголовков версий (##)")
        }

        return createResult(true, "Changelog имеет корректный формат")
    }
}
